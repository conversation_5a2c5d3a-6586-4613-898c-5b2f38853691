import React, { useState, useRef, useCallback, useEffect } from 'react';
import { Send, Paperclip, X, Upload, Check } from 'lucide-react';
import { Button } from '@/components/ui/button';


import { Progress } from '@/components/ui/progress';
import { MentionInput, type MentionInputRef } from '@/components/ui/mention-input';
import { EmojiPicker } from '@/components/ui/emoji-picker';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';

import { useTranslations } from '@/lib/i18n/typed-translations';
import { useWebSocket } from '@/lib/websocket';
import { useSendMessage } from '@/hooks/chat';
import { useUploadChatAttachment } from '@/hooks/chat';
import { useCreateComment } from '@/hooks/comments';
import { useAuth } from '@/contexts/auth-context';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';

interface UnifiedInputProps {
  variant: 'chat' | 'comment';
  hubId: number;

  // Chat-specific props
  channelId?: number; // required for chat

  // Comment-specific props
  postId?: number; // required for comment

  // Common props
  placeholder?: string;
  disabled?: boolean;
  className?: string;

  // Editing mode (for chat)
  isEditing?: boolean;
  editingContent?: string;
  editingAttachments?: Array<{
    url: string;
    filename: string;
    content_type?: string;
    size: number;
    type?: string;
  }>;
  onSaveEdit?: (content: string, attachmentUris?: string[]) => void;
  onCancelEdit?: () => void;

  // Callbacks
  onSuccess?: () => void; // called after successful send/post
}

interface AttachmentPreview {
  file?: File; // optional for existing attachments
  url?: string; // object URL for previews of new files
  isObjectUrl?: boolean; // whether url is an object URL that needs revocation
  uploading: boolean;
  uploadedUrl?: string; // final remote URL returned by backend (or existing)
  uploadedMetadata?: {
    url: string;
    filename: string;
    size: number;
    content_type: string;
    type: string;
  };
  error?: string;
}

export function UnifiedInput({
  variant,
  hubId,
  channelId,
  postId,
  placeholder,
  disabled,
  className,
  isEditing = false,
  editingContent = '',
  editingAttachments = [],
  onSaveEdit,
  onCancelEdit,
  onSuccess
}: UnifiedInputProps) {
  const { t, keys } = useTranslations();
  const { user } = useAuth();
  
  // Chat-specific hooks
  const { sendMessage: sendWebSocketMessage, sendTypingIndicator, isConnected } = useWebSocket();
  const sendMessageMutation = useSendMessage();
  const { uploadFile, isLoading: isUploading, progress } = useUploadChatAttachment();
  
  // Comment-specific hooks
  const createComment = useCreateComment();
  
  const [message, setMessage] = useState(''); // Email-based raw value (source of truth)
  const [attachments, setAttachments] = useState<AttachmentPreview[]>([]);
  const [isTyping, setIsTyping] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const inputRef = useRef<HTMLInputElement | HTMLTextAreaElement>(null);
  const mentionInputRef = useRef<MentionInputRef>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout>();
  const cursorPositionRef = useRef<{ start: number; end: number }>({ start: 0, end: 0 });

  // Initialize message content and attachments for editing mode
  useEffect(() => {
    if (isEditing) {
      setMessage(editingContent || '');

      // Initialize existing attachments into local state for full management
      setAttachments(() => {
        const initial: AttachmentPreview[] = (editingAttachments || []).map(a => ({
          uploading: false,
          uploadedUrl: a.url,
          uploadedMetadata: {
            url: a.url,
            filename: a.filename,
            size: a.size,
            content_type: a.content_type || 'application/octet-stream',
            type: a.type || 'file',
          },
        }));
        return initial;
      });
    } else {
      setMessage('');
      setAttachments(prev => {
        // Clean up object URLs when exiting editing mode
        prev.forEach(a => { if (a.isObjectUrl && a.url) URL.revokeObjectURL(a.url); });
        return [];
      });
    }
  }, [isEditing, editingContent, editingAttachments]);

  // Focus input when editing starts
  useEffect(() => {
    if (isEditing) {
      console.log('Edit mode started, attempting to focus input...');

      // Use a short delay to ensure dropdown is fully closed
      setTimeout(() => {
        console.log('Focus effect running after dropdown cleanup...');

        // For chat and comment variants, use MentionInput's focus method
        if ((variant === 'chat' || variant === 'comment') && mentionInputRef.current) {
          console.log('Focusing MentionInput...');
          mentionInputRef.current.focus();
        } else if (inputRef.current) {
          console.log('Focusing fallback input...');
          // Fallback for other variants
          inputRef.current.focus();
          // For contentEditable, move cursor to end
          if (inputRef.current.contentEditable === 'true') {
            const range = document.createRange();
            const selection = window.getSelection();
            range.selectNodeContents(inputRef.current);
            range.collapse(false);
            selection?.removeAllRanges();
            selection?.addRange(range);
          }
        }
      }, 200); // Wait for dropdown to close completely
    }
  }, [isEditing, variant]);

  // Handle typing indicators (chat only)
  const handleTyping = useCallback(() => {
    if (variant !== 'chat' || !channelId) return;
    
    if (!isTyping && isConnected) {
      setIsTyping(true);
      sendTypingIndicator(channelId);
    }

    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Set new timeout to stop typing indicator
    typingTimeoutRef.current = setTimeout(() => {
      setIsTyping(false);
    }, 2000);
  }, [variant, channelId, isTyping, isConnected, sendTypingIndicator]);

  // Cleanup typing timeout on unmount
  useEffect(() => {
    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    };
  }, []);

  // Store cursor position when input is focused or selection changes
  const updateCursorPosition = useCallback(() => {
    if (!inputRef.current) return;

    // For contenteditable elements (chat and comment variants), we don't need to track cursor position
    // since MentionInput handles its own cursor management
    if (variant === 'chat' || variant === 'comment') {
      return;
    }

    // For regular input/textarea elements, use selectionStart/selectionEnd
    const input = inputRef.current as HTMLInputElement | HTMLTextAreaElement;
    if ('selectionStart' in input && 'selectionEnd' in input) {
      const start = input.selectionStart || 0;
      const end = input.selectionEnd || 0;
      cursorPositionRef.current = { start, end };
    }
  }, [variant]);

  const handleEmojiSelect = useCallback((emoji: string) => {
    if (disabled) return;

    // Both chat and comment variants use MentionInput (contenteditable)
    // Use MentionInput's emoji insertion for both variants
    if ((variant === 'chat' || variant === 'comment') && mentionInputRef.current) {
      mentionInputRef.current.insertEmoji(emoji);
      return;
    }

    // Fallback for other variants that might use regular input elements
    // (Currently not used, but kept for future extensibility)
    if (!inputRef.current) return;

    const input = inputRef.current;
    const currentValue = message || '';

    // Use stored cursor position
    const { start, end } = cursorPositionRef.current;

    // Insert emoji at cursor position
    const newValue = currentValue.slice(0, start) + emoji + currentValue.slice(end);

    // Update the value
    setMessage(newValue);

    // Use setTimeout to ensure the DOM has updated before setting cursor position
    setTimeout(() => {
      if (input && inputRef.current && 'setSelectionRange' in input) {
        const newCursorPosition = start + emoji.length;
        input.focus();
        (input as HTMLInputElement | HTMLTextAreaElement).setSelectionRange(newCursorPosition, newCursorPosition);
        // Update stored cursor position
        cursorPositionRef.current = { start: newCursorPosition, end: newCursorPosition };
      }
    }, 0);
  }, [variant, message, disabled]);

  const handleFileSelect = useCallback(async (files: FileList) => {
    if (variant !== 'chat') return; // File attachments only for chat
    
    const newAttachments: AttachmentPreview[] = [];
    
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const preview: AttachmentPreview = {
        file,
        url: URL.createObjectURL(file),
        isObjectUrl: true,
        uploading: true,
      };
      newAttachments.push(preview);
    }

    setAttachments(prev => [...prev, ...newAttachments]);

    // Upload files
    for (let i = 0; i < newAttachments.length; i++) {
      const attachment = newAttachments[i];
      try {
        const result = await uploadFile(attachment.file);
        setAttachments(prev => prev.map(a =>
          a.file === attachment.file
            ? {
                ...a,
                uploading: false,
                uploadedUrl: result.url,
                uploadedMetadata: {
                  url: result.url,
                  filename: result.filename,
                  size: result.size,
                  content_type: result.mimeType,
                  type: result.type
                }
              }
            : a
        ));
      } catch (error) {
        setAttachments(prev => prev.map(a =>
          a.file === attachment.file
            ? { ...a, uploading: false, error: error instanceof Error ? error.message : 'Upload failed' }
            : a
        ));
      }
    }
  }, [variant, uploadFile]);

  const handleRemoveAttachment = useCallback((index: number) => {
    setAttachments(prev => {
      const attachment = prev[index];
      if (attachment.isObjectUrl && attachment.url) URL.revokeObjectURL(attachment.url);
      return prev.filter((_, i) => i !== index);
    });
  }, []);

  const handleSendMessage = useCallback(async () => {
    if (!message.trim() && attachments.length === 0) return;
    if (disabled) return;

    if (variant === 'chat') {
      if (!channelId || sendMessageMutation.isPending) return;
      
      // Check if any attachments are still uploading
      const uploadingAttachments = attachments.filter(a => a.uploading);
      if (uploadingAttachments.length > 0) return;

      // Check if any attachments failed to upload
      const failedAttachments = attachments.filter(a => a.error);
      if (failedAttachments.length > 0) return;

      const attachmentUris = attachments
        .filter(a => a.uploadedUrl)
        .map(a => a.uploadedUrl!);

      const attachmentMetadata = attachments
        .filter(a => a.uploadedMetadata)
        .map(a => a.uploadedMetadata!);

      const finalMessage = message.trim();
      try {
        // Use REST API when attachments are present to send complete metadata
        if (isConnected && attachmentMetadata.length === 0) {
          // Send via WebSocket for real-time delivery (text-only messages)
          sendWebSocketMessage(channelId, finalMessage, attachmentUris);
        } else {
          // Use REST API for messages with attachments or when WebSocket is not connected
          await sendMessageMutation.mutateAsync({
            params: { path: { channelId } },
            body: {
              content: finalMessage || "",
              attachment_uris: attachmentUris,
            },
          });
        }

        // Clear input and attachments
        setMessage('');
        setAttachments(prev => {
          prev.forEach(a => { if (a.isObjectUrl && a.url) URL.revokeObjectURL(a.url); });
          return [];
        });
        setIsTyping(false);
        if (typingTimeoutRef.current) {
          clearTimeout(typingTimeoutRef.current);
        }
        
        onSuccess?.();
      } catch (__error) {
        // Error handling for message sending
      }
    } else if (variant === 'comment') {
      if (!postId || createComment.isPending) return;
      
      try {
        await createComment.mutateAsync({
          params: { path: { postId } },
          body: { content: message.trim() }
        });
        
        setMessage('');
        onSuccess?.();
        toast.success(t(keys.collaborationHubs.posts.comments.commentPosted));
      } catch (__error) {
        toast.error(t(keys.collaborationHubs.posts.comments.failedToPost));
      }
    }
  }, [variant, message, attachments, disabled, channelId, postId, sendMessageMutation, createComment, isConnected, sendWebSocketMessage, onSuccess, t, keys]);

  const handleSaveEdit = useCallback(async () => {
    if (!message.trim() || !onSaveEdit) return;

    try {
      // When editing chat messages, pass current attachment URIs as well
      const attachmentUris = variant === 'chat'
        ? attachments.filter(a => a.uploadedUrl).map(a => a.uploadedUrl!)
        : undefined;

      await onSaveEdit(message.trim(), attachmentUris);
      // Clear the input after successful save
      setMessage('');
      setAttachments(prev => {
        prev.forEach(a => URL.revokeObjectURL(a.url));
        return [];
      });
    } catch (error) {
      // Error is handled by the parent component
      console.error('Failed to save edit:', error);
    }
  }, [message, onSaveEdit, variant, attachments]);

  const handleCancelEdit = useCallback(() => {
    if (onCancelEdit) {
      // Clear the input when canceling
      setMessage('');
      setAttachments(prev => {
        prev.forEach(a => { if (a.isObjectUrl && a.url) URL.revokeObjectURL(a.url); });
        return [];
      });
      onCancelEdit();
    }
  }, [onCancelEdit]);

  const handleKeyPress = useCallback((e: React.KeyboardEvent) => {
    if (variant === 'chat') {
      if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        if (isEditing) {
          handleSaveEdit();
        } else {
          handleSendMessage();
        }
      } else if (e.key === 'Escape' && isEditing) {
        e.preventDefault();
        handleCancelEdit();
      }
    } else if (variant === 'comment') {
      if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
        e.preventDefault();
        if (isEditing) {
          handleSaveEdit();
        } else {
          handleSendMessage();
        }
      }
    }
  }, [variant, handleSendMessage, handleSaveEdit, handleCancelEdit, isEditing]);

  const handleMessageChange = useCallback((newMessage: string) => {
    setMessage(newMessage);
    console.log(newMessage)
    if (variant === 'chat') {
      handleTyping();
    }
    updateCursorPosition();
  }, [variant, handleTyping, updateCursorPosition]);

  const getInitials = (name?: string) => {
    if (!name) return '??';
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const canSend = (message.trim() || attachments.some(a => a.uploadedUrl)) &&
                 !attachments.some(a => a.uploading) &&
                 !disabled &&
                 (variant === 'chat' ? !sendMessageMutation.isPending : !createComment.isPending);

  const canSaveEdit = isEditing && (!!message.trim() || attachments.some(a => a.uploadedUrl)) && !attachments.some(a => a.uploading);

  const getPlaceholder = () => {
    if (isEditing) return "Edit your message...";
    if (placeholder) return placeholder;
    if (variant === 'chat') {
      return t(keys.collaborationHubs.chat.messagePlaceholder, { channelName: 'channel' });
    }
    return t(keys.collaborationHubs.posts.comments.writeComment);
  };

  return (
    <div className={cn("space-y-2 w-full", className)}>
      {/* Show avatar for comments */}
      {variant === 'comment' && (
        <div className="flex gap-3 items-start">
          <Avatar className="h-8 w-8 flex-shrink-0">
            <AvatarFallback className="text-xs">
              {getInitials(user?.email || 'U')}
            </AvatarFallback>
          </Avatar>
          <div className="flex-1 space-y-2">
            {/* Input Container for comments */}
            <div className="relative w-full">
              <div className={cn(
                "flex items-start gap-2 px-4 py-3 border rounded-lg bg-background w-full min-w-0"
              )}>
                <MentionInput
                  ref={mentionInputRef}
                  hubId={hubId}
                  value={message}
                  onChange={handleMessageChange}
                  onKeyDown={handleKeyPress}
                  placeholder={getPlaceholder()}
                  disabled={disabled}
                  className="flex-1 min-w-0"
                >
                  {({
                    ref,
                    onChange,
                    onKeyDown,
                    onSelect,
                    placeholder,
                    disabled,
                    contentEditable,
                    suppressContentEditableWarning
                  }) => {
                    // Use callback ref pattern to handle both refs properly
                    const combinedRef = (el: HTMLDivElement | null) => {
                      // Set the MentionInput's ref if it exists and is mutable
                      if (ref && 'current' in ref && ref.current !== undefined) {
                        try {
                          (ref as React.MutableRefObject<HTMLDivElement | null>).current = el;
                        } catch (__error) {
                          // Ignore read-only ref errors
                        }
                      }
                      // Set our local ref (cast since we're changing from HTMLTextAreaElement to HTMLDivElement)
                      if (inputRef && 'current' in inputRef) {
                        try {
                          (inputRef as React.MutableRefObject<HTMLElement | null>).current = el;
                        } catch (__error) {
                          // Ignore read-only ref errors
                        }
                      }
                    };

                    const handleInput = (e: React.FormEvent<HTMLDivElement>) => {
                      onChange(e);
                      updateCursorPosition();
                    };

                    const handleSelect = (e: React.SyntheticEvent) => {
                      onSelect(e);
                      updateCursorPosition();
                    };

                    return (
                      <div
                        ref={combinedRef}
                        contentEditable={contentEditable}
                        suppressContentEditableWarning={suppressContentEditableWarning}
                        onInput={handleInput}
                        onKeyDown={onKeyDown}
                        onSelect={handleSelect}
                        onFocus={updateCursorPosition}
                        onClick={updateCursorPosition}
                        data-placeholder={placeholder}
                        dir="ltr"
                        className={cn(
                          "border-0 focus-visible:ring-0 px-0 py-0 min-h-[40px] bg-transparent dark:bg-transparent outline-none text-sm",
                          "empty:before:content-[attr(data-placeholder)] empty:before:text-muted-foreground empty:before:pointer-events-none",
                          disabled && "pointer-events-none opacity-50"
                        )}
                        style={{
                          direction: 'ltr',
                          textAlign: 'left',
                          unicodeBidi: 'embed',
                          wordBreak: 'break-word',
                          whiteSpace: 'pre-wrap'
                        }}
                      />
                    );
                  }}
                </MentionInput>

                {/* Emoji Button */}
                <EmojiPicker
                  onEmojiSelect={handleEmojiSelect}
                  disabled={disabled}
                  title={t(keys.ui.emojiPicker.addEmoji)}
                />

                {/* Action Buttons */}
                {isEditing ? (
                  <div className="flex items-center gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0 shrink-0 bg-white hover:bg-gray-50 text-gray-500 hover:text-gray-700 border border-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 dark:text-gray-400 dark:hover:text-gray-200 dark:border-gray-600"
                      onClick={handleCancelEdit}
                      title={t(keys.common.cancel)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0 shrink-0 bg-white hover:bg-green-50 text-green-600 hover:text-green-700 border border-green-200 dark:bg-green-900/20 dark:hover:bg-green-900/30 dark:text-green-400 dark:hover:text-green-300 dark:border-green-800"
                      onClick={handleSaveEdit}
                      disabled={!message.trim()}
                      title={t(keys.common.save)}
                    >
                      <Check className="h-4 w-4" />
                    </Button>
                  </div>
                ) : (
                  <Button
                    onClick={handleSendMessage}
                    disabled={!canSend}
                    className="h-8 w-8 p-0 shrink-0 bg-white hover:bg-blue-50 text-blue-600 hover:text-blue-700 border border-blue-200 dark:bg-blue-900/20 dark:hover:bg-blue-900/30 dark:text-blue-400 dark:hover:text-blue-300 dark:border-blue-800"
                    size="sm"
                    title={t(keys.collaborationHubs.posts.comments.postComment)}
                  >
                    <Send className="h-4 w-4" />
                  </Button>
                )}
              </div>

              {/* Helper text for comments */}
              <div className="text-xs text-muted-foreground mt-1">
                {t(keys.collaborationHubs.posts.comments.ctrlEnterToPost)}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Attachment Previews (chat only) */}
      {variant === 'chat' && attachments.length > 0 && (
        <div className="flex flex-wrap gap-2 p-2 border rounded-lg bg-muted/30 max-h-32 overflow-y-auto">
          {attachments.map((attachment, index) => (
            <div key={index} className="relative flex items-center gap-2 p-2 border rounded bg-background">
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium truncate">{attachment.uploadedMetadata?.filename || attachment.file?.name || 'attachment'}</p>
                {attachment.uploading && (
                  <div className="space-y-1">
                    <Progress value={progress} className="h-1" />
                    <p className="text-xs text-muted-foreground">{t(keys.collaborationHubs.chat.uploading)}</p>
                  </div>
                )}
                {attachment.error && (
                  <p className="text-xs text-destructive">{attachment.error}</p>
                )}
              </div>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0"
                onClick={() => handleRemoveAttachment(index)}
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
          ))}
        </div>
      )}

      {/* Chat Input Container */}
      {variant === 'chat' && (
        <div className="relative w-full">
          <div className={cn(
            "flex items-center gap-2 px-4 py-3 border rounded-lg bg-background w-full min-w-0",
            isEditing && "border-blue-300 dark:border-blue-600 bg-blue-50/50 dark:bg-blue-950/20"
          )}>
            <MentionInput
              ref={mentionInputRef}
              hubId={hubId}
              value={message}
              onChange={handleMessageChange}
              onKeyDown={handleKeyPress}
              placeholder={getPlaceholder()}
              disabled={disabled}
              className="flex-1 min-w-0"
            >
              {({
                ref,
                onChange,
                onKeyDown,
                onSelect,
                onBeforeInput,
                placeholder,
                disabled,
                contentEditable,
                suppressContentEditableWarning
              }) => {
                // Use callback ref pattern to handle both refs properly
                const combinedRef = (el: HTMLDivElement | null) => {
                  // Set the MentionInput's ref if it exists and is mutable
                  if (ref && 'current' in ref && ref.current !== undefined) {
                    try {
                      (ref as React.MutableRefObject<HTMLDivElement | null>).current = el;
                    } catch (__error) {
                      // Ignore read-only ref errors
                    }
                  }
                  // Set our local ref (cast since we're changing from HTMLInputElement to HTMLDivElement)
                  if (inputRef && 'current' in inputRef) {
                    try {
                      (inputRef as React.MutableRefObject<HTMLElement | null>).current = el;
                    } catch (__error) {
                      // Ignore read-only ref errors
                    }
                  }
                };

                const handleInput = (e: React.FormEvent<HTMLDivElement>) => {
                  onChange(e);
                  updateCursorPosition();
                };

                const handleSelect = (e: React.SyntheticEvent) => {
                  onSelect(e);
                  updateCursorPosition();
                };

                return (
                  <div
                    ref={combinedRef}
                    contentEditable={contentEditable}
                    suppressContentEditableWarning={suppressContentEditableWarning}
                    onInput={handleInput}
                    onBeforeInput={onBeforeInput}
                    onKeyDown={onKeyDown}
                    onSelect={handleSelect}
                    onFocus={updateCursorPosition}
                    onClick={updateCursorPosition}
                    data-placeholder={placeholder}
                    dir="ltr"
                    className={cn(
                      "border-0 focus-visible:ring-0 px-0 py-0 h-auto bg-transparent dark:bg-transparent min-h-[24px] outline-none text-sm",
                      "empty:before:content-[attr(data-placeholder)] empty:before:text-muted-foreground empty:before:pointer-events-none",
                      isEditing && "empty:before:text-blue-500/70 dark:empty:before:text-blue-400/70",
                      disabled && "pointer-events-none opacity-50"
                    )}
                    style={{
                      direction: 'ltr',
                      textAlign: 'left',
                      unicodeBidi: 'embed',
                      wordBreak: 'break-word',
                      whiteSpace: 'pre-wrap'
                    }}
                  />
                );
              }}
            </MentionInput>

            {/* File Upload Button - Chat only (also during editing) */}
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 shrink-0 hover:bg-muted"
              onClick={() => fileInputRef.current?.click()}
              disabled={disabled || isUploading}
              title={t(keys.collaborationHubs.chat.attachFile)}
            >
              {isUploading ? <Upload className="h-4 w-4 animate-spin" /> : <Paperclip className="h-4 w-4" />}
            </Button>

            {/* Emoji Button (also during editing) */}
            <EmojiPicker
              onEmojiSelect={handleEmojiSelect}
              disabled={disabled}
              title={t(keys.collaborationHubs.chat.addEmoji)}
            />

            {/* Editing Mode Buttons (Chat only) */}
            {isEditing ? (
              <>
                {/* Subtle editing indicator */}
                <div className="h-2 w-2 bg-blue-500 rounded-full animate-pulse shrink-0" title="Editing message" />

                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0 shrink-0 bg-white hover:bg-gray-50 text-gray-500 hover:text-gray-700 border border-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 dark:text-gray-400 dark:hover:text-gray-200 dark:border-gray-600"
                  onClick={handleCancelEdit}
                  title="Cancel editing (Esc)"
                >
                  <X className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0 shrink-0 bg-white hover:bg-green-50 text-green-600 hover:text-green-700 border border-green-200 dark:bg-green-900/20 dark:hover:bg-green-900/30 dark:text-green-400 dark:hover:text-green-300 dark:border-green-800"
                  onClick={handleSaveEdit}
                  disabled={!canSaveEdit}
                  title="Save changes (Enter)"
                >
                  <Check className="h-4 w-4" />
                </Button>
              </>
            ) : (
              /* Send Button */
              <Button
                onClick={handleSendMessage}
                disabled={!canSend}
                className="h-8 w-8 p-0 shrink-0 bg-white hover:bg-blue-50 text-blue-600 hover:text-blue-700 border border-blue-200 dark:bg-blue-900/20 dark:hover:bg-blue-900/30 dark:text-blue-400 dark:hover:text-blue-300 dark:border-blue-800"
                size="sm"
                title={t(keys.collaborationHubs.chat.send)}
              >
                <Send className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
      )}

      {/* Hidden File Input (Chat only) */}
      {variant === 'chat' && (
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept="image/*,video/*,.pdf,.doc,.docx,.txt"
          onChange={(e) => e.target.files && handleFileSelect(e.target.files)}
          className="hidden"
        />
      )}
    </div>
  );
}
